{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\pages\\\\DoctorProfilePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { FiMessageCircle, FiVideo, FiPhone } from 'react-icons/fi';\nimport { doctorAPI } from '../services/api';\nimport BookingForm from '../components/BookingForm';\nimport CommunicationDashboard from '../components/CommunicationDashboard';\nimport { useAuth } from '../context/AuthContext';\nimport { useSocket } from '../context/SocketContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoctorProfilePage = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [doctor, setDoctor] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [bookingSuccess, setBookingSuccess] = useState(false);\n  const [showCommunications, setShowCommunications] = useState(false);\n  const {\n    user,\n    isAuthenticated\n  } = useAuth();\n  const {\n    initiateCall\n  } = useSocket();\n  useEffect(() => {\n    fetchDoctorProfile();\n  }, [id]);\n  const fetchDoctorProfile = async () => {\n    try {\n      setLoading(true);\n      const response = await doctorAPI.getDoctorById(id);\n      setDoctor(response.data.data.doctor);\n    } catch (error) {\n      setError('Doctor not found or not available');\n      console.error('Error fetching doctor:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBookingSuccess = () => {\n    setBookingSuccess(true);\n    setTimeout(() => {\n      setBookingSuccess(false);\n    }, 5000);\n  };\n  const handleStartCall = callType => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    initiateCall(doctor.user._id, callType);\n  };\n  const handleStartChat = () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    setShowCommunications(true);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !doctor) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDE1E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-2\",\n          children: \"Doctor Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/'),\n          className: \"btn-primary\",\n          children: \"Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [bookingSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-green-50 border border-green-200 rounded-lg p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-green-600 mr-3\",\n            children: \"\\u2705\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-green-800 font-semibold\",\n              children: \"Appointment Booked Successfully!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-700 text-sm\",\n              children: \"Your appointment has been booked. You can view it in your dashboard.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-6 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: doctor.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: doctor.profileImage,\n                  alt: doctor.user.name,\n                  className: \"w-24 h-24 rounded-full object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-3xl font-semibold text-gray-600\",\n                  children: doctor.user.name.charAt(0).toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl font-bold text-gray-900 mb-2\",\n                  children: [\"Dr. \", doctor.user.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium\",\n                    children: doctor.specialty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: [doctor.experience, \" years experience\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-green-600 font-medium\",\n                    children: \"Available for appointments\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-gray-900\",\n                  children: [\"$\", doctor.consultationFee, /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-base font-normal text-gray-600 ml-1\",\n                    children: \"consultation fee\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), isAuthenticated && (user === null || user === void 0 ? void 0 : user.role) === 'patient' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 mt-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleStartChat,\n                    className: \"flex items-center space-x-2 px-3 py-2 bg-primary-100 text-primary-700 rounded-lg hover:bg-primary-200 transition-colors\",\n                    title: \"Start chat\",\n                    children: [/*#__PURE__*/_jsxDEV(FiMessageCircle, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm\",\n                      children: \"Chat\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleStartCall('audio'),\n                    className: \"flex items-center space-x-2 px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors\",\n                    title: \"Audio call\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm\",\n                      children: \"Call\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleStartCall('video'),\n                    className: \"flex items-center space-x-2 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors\",\n                    title: \"Video call\",\n                    children: [/*#__PURE__*/_jsxDEV(FiVideo, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm\",\n                      children: \"Video\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                children: \"Qualifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 leading-relaxed\",\n                children: Array.isArray(doctor.qualifications) ? doctor.qualifications.join(', ') : doctor.qualifications\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), doctor.bio && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                children: \"About\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 leading-relaxed\",\n                children: doctor.bio\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                children: \"Availability\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid md:grid-cols-2 gap-4\",\n                children: doctor.availableDays && doctor.availableDays.length > 0 ? doctor.availableDays.map(day => {\n                  var _doctor$availableTime, _doctor$availableTime2;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-lg border bg-green-50 border-green-200\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium text-gray-900\",\n                        children: day\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-600 text-sm\",\n                        children: [((_doctor$availableTime = doctor.availableTime) === null || _doctor$availableTime === void 0 ? void 0 : _doctor$availableTime.start) || '09:00', \" - \", ((_doctor$availableTime2 = doctor.availableTime) === null || _doctor$availableTime2 === void 0 ? void 0 : _doctor$availableTime2.end) || '17:00']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 25\n                    }, this)\n                  }, day, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 23\n                  }, this);\n                }) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-span-2 p-3 rounded-lg border bg-gray-50 border-gray-200\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"Availability information not provided\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-t pt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                children: \"Contact Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"\\uD83D\\uDCE7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-700\",\n                    children: doctor.user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sticky top-8\",\n            children: /*#__PURE__*/_jsxDEV(BookingForm, {\n              doctorId: doctor.user._id,\n              onBookingSuccess: handleBookingSuccess\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CommunicationDashboard, {\n      isOpen: showCommunications,\n      onClose: () => setShowCommunications(false),\n      initialReceiver: doctor === null || doctor === void 0 ? void 0 : doctor.user\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(DoctorProfilePage, \"zidV+x3V+bpKKTIkRFo1JK5nO3w=\", false, function () {\n  return [useParams, useNavigate, useAuth, useSocket];\n});\n_c = DoctorProfilePage;\nexport default DoctorProfilePage;\nvar _c;\n$RefreshReg$(_c, \"DoctorProfilePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "FiMessageCircle", "FiVideo", "FiPhone", "doctor<PERSON><PERSON>", "BookingForm", "CommunicationDashboard", "useAuth", "useSocket", "jsxDEV", "_jsxDEV", "DoctorProfilePage", "_s", "id", "navigate", "doctor", "setDoctor", "loading", "setLoading", "error", "setError", "bookingSuccess", "setBookingSuccess", "showCommunications", "setShowCommunications", "user", "isAuthenticated", "initiateCall", "fetchDoctorProfile", "response", "getDoctorById", "data", "console", "handleBookingSuccess", "setTimeout", "handleStartCall", "callType", "_id", "handleStartChat", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "profileImage", "src", "alt", "name", "char<PERSON>t", "toUpperCase", "specialty", "experience", "consultationFee", "role", "title", "size", "Array", "isArray", "qualifications", "join", "bio", "availableDays", "length", "map", "day", "_doctor$availableTime", "_doctor$availableTime2", "availableTime", "start", "end", "email", "doctorId", "onBookingSuccess", "isOpen", "onClose", "initialReceiver", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/pages/DoctorProfilePage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { FiMessageCircle, FiVideo, FiPhone } from 'react-icons/fi';\nimport { doctorAPI } from '../services/api';\nimport BookingForm from '../components/BookingForm';\nimport CommunicationDashboard from '../components/CommunicationDashboard';\nimport { useAuth } from '../context/AuthContext';\nimport { useSocket } from '../context/SocketContext';\n\nconst DoctorProfilePage = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const [doctor, setDoctor] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [bookingSuccess, setBookingSuccess] = useState(false);\n  const [showCommunications, setShowCommunications] = useState(false);\n\n  const { user, isAuthenticated } = useAuth();\n  const { initiateCall } = useSocket();\n\n  useEffect(() => {\n    fetchDoctorProfile();\n  }, [id]);\n\n  const fetchDoctorProfile = async () => {\n    try {\n      setLoading(true);\n      const response = await doctorAPI.getDoctorById(id);\n      setDoctor(response.data.data.doctor);\n    } catch (error) {\n      setError('Doctor not found or not available');\n      console.error('Error fetching doctor:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBookingSuccess = () => {\n    setBookingSuccess(true);\n    setTimeout(() => {\n      setBookingSuccess(false);\n    }, 5000);\n  };\n\n\n\n  const handleStartCall = (callType) => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    initiateCall(doctor.user._id, callType);\n  };\n\n  const handleStartChat = () => {\n    if (!isAuthenticated) {\n      navigate('/login');\n      return;\n    }\n    setShowCommunications(true);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  if (error || !doctor) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">😞</div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Doctor Not Found</h2>\n          <p className=\"text-gray-600 mb-4\">{error}</p>\n          <button \n            onClick={() => navigate('/')}\n            className=\"btn-primary\"\n          >\n            Back to Home\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Success Message */}\n        {bookingSuccess && (\n          <div className=\"mb-6 bg-green-50 border border-green-200 rounded-lg p-4\">\n            <div className=\"flex items-center\">\n              <div className=\"text-green-600 mr-3\">✅</div>\n              <div>\n                <h4 className=\"text-green-800 font-semibold\">Appointment Booked Successfully!</h4>\n                <p className=\"text-green-700 text-sm\">\n                  Your appointment has been booked. You can view it in your dashboard.\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Doctor Information */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"card\">\n              {/* Doctor Header */}\n              <div className=\"flex items-start space-x-6 mb-6\">\n                <div className=\"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0\">\n                  {doctor.profileImage ? (\n                    <img \n                      src={doctor.profileImage} \n                      alt={doctor.user.name}\n                      className=\"w-24 h-24 rounded-full object-cover\"\n                    />\n                  ) : (\n                    <span className=\"text-3xl font-semibold text-gray-600\">\n                      {doctor.user.name.charAt(0).toUpperCase()}\n                    </span>\n                  )}\n                </div>\n\n                <div className=\"flex-1\">\n                  <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                    Dr. {doctor.user.name}\n                  </h1>\n                  \n                  <div className=\"flex items-center space-x-4 mb-3\">\n                    <span className=\"bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium\">\n                      {doctor.specialty}\n                    </span>\n                    <span className=\"text-gray-600\">\n                      {doctor.experience} years experience\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2 mb-4\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-green-600 font-medium\">Available for appointments</span>\n                  </div>\n\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    ${doctor.consultationFee}\n                    <span className=\"text-base font-normal text-gray-600 ml-1\">\n                      consultation fee\n                    </span>\n                  </div>\n\n                  {/* Quick Communication Actions */}\n                  {isAuthenticated && user?.role === 'patient' && (\n                    <div className=\"flex items-center space-x-2 mt-4\">\n                      <button\n                        onClick={handleStartChat}\n                        className=\"flex items-center space-x-2 px-3 py-2 bg-primary-100 text-primary-700 rounded-lg hover:bg-primary-200 transition-colors\"\n                        title=\"Start chat\"\n                      >\n                        <FiMessageCircle size={16} />\n                        <span className=\"text-sm\">Chat</span>\n                      </button>\n                      <button\n                        onClick={() => handleStartCall('audio')}\n                        className=\"flex items-center space-x-2 px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors\"\n                        title=\"Audio call\"\n                      >\n                        <FiPhone size={16} />\n                        <span className=\"text-sm\">Call</span>\n                      </button>\n                      <button\n                        onClick={() => handleStartCall('video')}\n                        className=\"flex items-center space-x-2 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors\"\n                        title=\"Video call\"\n                      >\n                        <FiVideo size={16} />\n                        <span className=\"text-sm\">Video</span>\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Qualifications */}\n              <div className=\"mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Qualifications</h3>\n                <p className=\"text-gray-700 leading-relaxed\">\n                  {Array.isArray(doctor.qualifications)\n                    ? doctor.qualifications.join(', ')\n                    : doctor.qualifications}\n                </p>\n              </div>\n\n              {/* About */}\n              {doctor.bio && (\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">About</h3>\n                  <p className=\"text-gray-700 leading-relaxed\">\n                    {doctor.bio}\n                  </p>\n                </div>\n              )}\n\n              {/* Availability */}\n              <div className=\"mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Availability</h3>\n                <div className=\"grid md:grid-cols-2 gap-4\">\n                  {doctor.availableDays && doctor.availableDays.length > 0 ? (\n                    doctor.availableDays.map((day) => (\n                      <div\n                        key={day}\n                        className=\"p-3 rounded-lg border bg-green-50 border-green-200\"\n                      >\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"font-medium text-gray-900\">{day}</span>\n                          <span className=\"text-green-600 text-sm\">\n                            {doctor.availableTime?.start || '09:00'} - {doctor.availableTime?.end || '17:00'}\n                          </span>\n                        </div>\n                      </div>\n                    ))\n                  ) : (\n                    <div className=\"col-span-2 p-3 rounded-lg border bg-gray-50 border-gray-200\">\n                      <span className=\"text-gray-500\">Availability information not provided</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Contact Information */}\n              <div className=\"border-t pt-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Contact Information</h3>\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-gray-500\">📧</span>\n                    <span className=\"text-gray-700\">{doctor.user.email}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Booking Form */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"sticky top-8\">\n              <BookingForm \n                doctorId={doctor.user._id} \n                onBookingSuccess={handleBookingSuccess}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Communication Dashboard */}\n      <CommunicationDashboard\n        isOpen={showCommunications}\n        onClose={() => setShowCommunications(false)}\n        initialReceiver={doctor?.user}\n      />\n    </div>\n  );\n};\n\nexport default DoctorProfilePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,eAAe,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAG,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC1B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAM;IAAE4B,IAAI;IAAEC;EAAgB,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEoB;EAAa,CAAC,GAAGnB,SAAS,CAAC,CAAC;EAEpCV,SAAS,CAAC,MAAM;IACd8B,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACf,EAAE,CAAC,CAAC;EAER,MAAMe,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMW,QAAQ,GAAG,MAAMzB,SAAS,CAAC0B,aAAa,CAACjB,EAAE,CAAC;MAClDG,SAAS,CAACa,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAChB,MAAM,CAAC;IACtC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,QAAQ,CAAC,mCAAmC,CAAC;MAC7CY,OAAO,CAACb,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,oBAAoB,GAAGA,CAAA,KAAM;IACjCX,iBAAiB,CAAC,IAAI,CAAC;IACvBY,UAAU,CAAC,MAAM;MACfZ,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAID,MAAMa,eAAe,GAAIC,QAAQ,IAAK;IACpC,IAAI,CAACV,eAAe,EAAE;MACpBZ,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IACAa,YAAY,CAACZ,MAAM,CAACU,IAAI,CAACY,GAAG,EAAED,QAAQ,CAAC;EACzC,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACZ,eAAe,EAAE;MACpBZ,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IACAU,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,IAAIP,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK6B,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE9B,OAAA;QAAK6B,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,IAAIzB,KAAK,IAAI,CAACJ,MAAM,EAAE;IACpB,oBACEL,OAAA;MAAK6B,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE9B,OAAA;QAAK6B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9B,OAAA;UAAK6B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvClC,OAAA;UAAI6B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ElC,OAAA;UAAG6B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAErB;QAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7ClC,OAAA;UACEmC,OAAO,EAAEA,CAAA,KAAM/B,QAAQ,CAAC,GAAG,CAAE;UAC7ByB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElC,OAAA;IAAK6B,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAC3C9B,OAAA;MAAK6B,SAAS,EAAC,wCAAwC;MAAAC,QAAA,GAEpDnB,cAAc,iBACbX,OAAA;QAAK6B,SAAS,EAAC,yDAAyD;QAAAC,QAAA,eACtE9B,OAAA;UAAK6B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9B,OAAA;YAAK6B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5ClC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAI6B,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFlC,OAAA;cAAG6B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDlC,OAAA;QAAK6B,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAExC9B,OAAA;UAAK6B,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9B,OAAA;YAAK6B,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAEnB9B,OAAA;cAAK6B,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C9B,OAAA;gBAAK6B,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,EAC/FzB,MAAM,CAAC+B,YAAY,gBAClBpC,OAAA;kBACEqC,GAAG,EAAEhC,MAAM,CAAC+B,YAAa;kBACzBE,GAAG,EAAEjC,MAAM,CAACU,IAAI,CAACwB,IAAK;kBACtBV,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,gBAEFlC,OAAA;kBAAM6B,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EACnDzB,MAAM,CAACU,IAAI,CAACwB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlC,OAAA;gBAAK6B,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB9B,OAAA;kBAAI6B,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,GAAC,MAChD,EAACzB,MAAM,CAACU,IAAI,CAACwB,IAAI;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eAELlC,OAAA;kBAAK6B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/C9B,OAAA;oBAAM6B,SAAS,EAAC,4EAA4E;oBAAAC,QAAA,EACzFzB,MAAM,CAACqC;kBAAS;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACPlC,OAAA;oBAAM6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAC5BzB,MAAM,CAACsC,UAAU,EAAC,mBACrB;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAENlC,OAAA;kBAAK6B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/C9B,OAAA;oBAAK6B,SAAS,EAAC;kBAAmC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzDlC,OAAA;oBAAM6B,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eAENlC,OAAA;kBAAK6B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,GAC/C,EAACzB,MAAM,CAACuC,eAAe,eACxB5C,OAAA;oBAAM6B,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAE3D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EAGLlB,eAAe,IAAI,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,MAAK,SAAS,iBAC1C7C,OAAA;kBAAK6B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/C9B,OAAA;oBACEmC,OAAO,EAAEP,eAAgB;oBACzBC,SAAS,EAAC,yHAAyH;oBACnIiB,KAAK,EAAC,YAAY;oBAAAhB,QAAA,gBAElB9B,OAAA,CAACT,eAAe;sBAACwD,IAAI,EAAE;oBAAG;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7BlC,OAAA;sBAAM6B,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACTlC,OAAA;oBACEmC,OAAO,EAAEA,CAAA,KAAMV,eAAe,CAAC,OAAO,CAAE;oBACxCI,SAAS,EAAC,mHAAmH;oBAC7HiB,KAAK,EAAC,YAAY;oBAAAhB,QAAA,gBAElB9B,OAAA,CAACP,OAAO;sBAACsD,IAAI,EAAE;oBAAG;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrBlC,OAAA;sBAAM6B,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACTlC,OAAA;oBACEmC,OAAO,EAAEA,CAAA,KAAMV,eAAe,CAAC,OAAO,CAAE;oBACxCI,SAAS,EAAC,gHAAgH;oBAC1HiB,KAAK,EAAC,YAAY;oBAAAhB,QAAA,gBAElB9B,OAAA,CAACR,OAAO;sBAACuD,IAAI,EAAE;oBAAG;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrBlC,OAAA;sBAAM6B,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlC,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9B,OAAA;gBAAI6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5ElC,OAAA;gBAAG6B,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EACzCkB,KAAK,CAACC,OAAO,CAAC5C,MAAM,CAAC6C,cAAc,CAAC,GACjC7C,MAAM,CAAC6C,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,GAChC9C,MAAM,CAAC6C;cAAc;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EAGL7B,MAAM,CAAC+C,GAAG,iBACTpD,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9B,OAAA;gBAAI6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnElC,OAAA;gBAAG6B,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EACzCzB,MAAM,CAAC+C;cAAG;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN,eAGDlC,OAAA;cAAK6B,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB9B,OAAA;gBAAI6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1ElC,OAAA;gBAAK6B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACvCzB,MAAM,CAACgD,aAAa,IAAIhD,MAAM,CAACgD,aAAa,CAACC,MAAM,GAAG,CAAC,GACtDjD,MAAM,CAACgD,aAAa,CAACE,GAAG,CAAEC,GAAG;kBAAA,IAAAC,qBAAA,EAAAC,sBAAA;kBAAA,oBAC3B1D,OAAA;oBAEE6B,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,eAE9D9B,OAAA;sBAAK6B,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChD9B,OAAA;wBAAM6B,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAE0B;sBAAG;wBAAAzB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACxDlC,OAAA;wBAAM6B,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,GACrC,EAAA2B,qBAAA,GAAApD,MAAM,CAACsD,aAAa,cAAAF,qBAAA,uBAApBA,qBAAA,CAAsBG,KAAK,KAAI,OAAO,EAAC,KAAG,EAAC,EAAAF,sBAAA,GAAArD,MAAM,CAACsD,aAAa,cAAAD,sBAAA,uBAApBA,sBAAA,CAAsBG,GAAG,KAAI,OAAO;sBAAA;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5E,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC,GARDsB,GAAG;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OASL,CAAC;gBAAA,CACP,CAAC,gBAEFlC,OAAA;kBAAK6B,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,eAC1E9B,OAAA;oBAAM6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAqC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlC,OAAA;cAAK6B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B9B,OAAA;gBAAI6B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFlC,OAAA;gBAAK6B,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxB9B,OAAA;kBAAK6B,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C9B,OAAA;oBAAM6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzClC,OAAA;oBAAM6B,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEzB,MAAM,CAACU,IAAI,CAAC+C;kBAAK;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlC,OAAA;UAAK6B,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9B,OAAA;YAAK6B,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B9B,OAAA,CAACL,WAAW;cACVoE,QAAQ,EAAE1D,MAAM,CAACU,IAAI,CAACY,GAAI;cAC1BqC,gBAAgB,EAAEzC;YAAqB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA,CAACJ,sBAAsB;MACrBqE,MAAM,EAAEpD,kBAAmB;MAC3BqD,OAAO,EAAEA,CAAA,KAAMpD,qBAAqB,CAAC,KAAK,CAAE;MAC5CqD,eAAe,EAAE9D,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEU;IAAK;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAChC,EAAA,CA/PID,iBAAiB;EAAA,QACNZ,SAAS,EACPC,WAAW,EAOMO,OAAO,EAChBC,SAAS;AAAA;AAAAsE,EAAA,GAV9BnE,iBAAiB;AAiQvB,eAAeA,iBAAiB;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}