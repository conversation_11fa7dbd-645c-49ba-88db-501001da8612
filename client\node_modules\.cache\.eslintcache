[{"E:\\03\\client\\src\\index.js": "1", "E:\\03\\client\\src\\App.js": "2", "E:\\03\\client\\src\\context\\AuthContext.jsx": "3", "E:\\03\\client\\src\\components\\ProtectedRoute.jsx": "4", "E:\\03\\client\\src\\components\\Navbar.jsx": "5", "E:\\03\\client\\src\\pages\\HomePage.jsx": "6", "E:\\03\\client\\src\\pages\\LoginPage.jsx": "7", "E:\\03\\client\\src\\pages\\DoctorProfilePage.jsx": "8", "E:\\03\\client\\src\\pages\\PatientDashboard.jsx": "9", "E:\\03\\client\\src\\pages\\DoctorDashboard.jsx": "10", "E:\\03\\client\\src\\pages\\RegisterPage.jsx": "11", "E:\\03\\client\\src\\services\\api.js": "12", "E:\\03\\client\\src\\components\\DoctorCard.jsx": "13", "E:\\03\\client\\src\\components\\BookingForm.jsx": "14", "E:\\03\\client\\src\\context\\SocketContext.jsx": "15", "E:\\03\\client\\src\\components\\NotificationCenter.jsx": "16", "E:\\03\\client\\src\\components\\HelplineSupport.jsx": "17", "E:\\03\\client\\src\\components\\CommunicationDashboard.jsx": "18", "E:\\03\\client\\src\\components\\VideoCall.jsx": "19", "E:\\03\\client\\src\\components\\ChatInterface.jsx": "20", "E:\\03\\client\\src\\hooks\\useWebRTC.js": "21", "E:\\03\\client\\src\\components\\CommunicationTest.jsx": "22", "E:\\03\\client\\src\\components\\LoadingSpinner.jsx": "23"}, {"size": 254, "mtime": 1751819478486, "results": "24", "hashOfConfig": "25"}, {"size": 2919, "mtime": 1751981309044, "results": "26", "hashOfConfig": "25"}, {"size": 6359, "mtime": 1751981072627, "results": "27", "hashOfConfig": "25"}, {"size": 838, "mtime": 1751819460972, "results": "28", "hashOfConfig": "25"}, {"size": 5522, "mtime": 1751981212713, "results": "29", "hashOfConfig": "25"}, {"size": 7108, "mtime": 1751819217590, "results": "30", "hashOfConfig": "25"}, {"size": 4967, "mtime": 1751819236976, "results": "31", "hashOfConfig": "25"}, {"size": 10230, "mtime": 1751825273427, "results": "32", "hashOfConfig": "25"}, {"size": 10651, "mtime": 1751819402216, "results": "33", "hashOfConfig": "25"}, {"size": 13478, "mtime": 1751819445479, "results": "34", "hashOfConfig": "25"}, {"size": 14901, "mtime": 1751819279719, "results": "35", "hashOfConfig": "25"}, {"size": 3711, "mtime": 1752083580631, "results": "36", "hashOfConfig": "25"}, {"size": 2551, "mtime": 1752086070662, "results": "37", "hashOfConfig": "25"}, {"size": 6520, "mtime": 1751819328878, "results": "38", "hashOfConfig": "25"}, {"size": 9250, "mtime": 1751981175952, "results": "39", "hashOfConfig": "25"}, {"size": 6777, "mtime": 1751825065593, "results": "40", "hashOfConfig": "25"}, {"size": 16707, "mtime": 1751979689375, "results": "41", "hashOfConfig": "25"}, {"size": 12611, "mtime": 1751979675030, "results": "42", "hashOfConfig": "25"}, {"size": 8060, "mtime": 1751979767707, "results": "43", "hashOfConfig": "25"}, {"size": 10759, "mtime": 1751979602200, "results": "44", "hashOfConfig": "25"}, {"size": 9622, "mtime": 1751824852519, "results": "45", "hashOfConfig": "25"}, {"size": 7140, "mtime": 1751826548655, "results": "46", "hashOfConfig": "25"}, {"size": 553, "mtime": 1751981225908, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1syuwl8", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\03\\client\\src\\index.js", [], [], "E:\\03\\client\\src\\App.js", [], [], "E:\\03\\client\\src\\context\\AuthContext.jsx", [], [], "E:\\03\\client\\src\\components\\ProtectedRoute.jsx", [], [], "E:\\03\\client\\src\\components\\Navbar.jsx", ["117"], [], "E:\\03\\client\\src\\pages\\HomePage.jsx", ["118"], [], "E:\\03\\client\\src\\pages\\LoginPage.jsx", [], [], "E:\\03\\client\\src\\pages\\DoctorProfilePage.jsx", ["119", "120"], [], "E:\\03\\client\\src\\pages\\PatientDashboard.jsx", [], [], "E:\\03\\client\\src\\pages\\DoctorDashboard.jsx", [], [], "E:\\03\\client\\src\\pages\\RegisterPage.jsx", [], [], "E:\\03\\client\\src\\services\\api.js", [], [], "E:\\03\\client\\src\\components\\DoctorCard.jsx", [], [], "E:\\03\\client\\src\\components\\BookingForm.jsx", ["121"], [], "E:\\03\\client\\src\\context\\SocketContext.jsx", ["122"], [], "E:\\03\\client\\src\\components\\NotificationCenter.jsx", [], [], "E:\\03\\client\\src\\components\\HelplineSupport.jsx", ["123", "124"], [], "E:\\03\\client\\src\\components\\CommunicationDashboard.jsx", ["125"], [], "E:\\03\\client\\src\\components\\VideoCall.jsx", ["126"], [], "E:\\03\\client\\src\\components\\ChatInterface.jsx", ["127"], [], "E:\\03\\client\\src\\hooks\\useWebRTC.js", ["128"], [], "E:\\03\\client\\src\\components\\CommunicationTest.jsx", [], [], "E:\\03\\client\\src\\components\\LoadingSpinner.jsx", [], [], {"ruleId": "129", "severity": 1, "message": "130", "line": 3, "column": 27, "nodeType": "131", "messageId": "132", "endLine": 3, "endColumn": 34}, {"ruleId": "133", "severity": 1, "message": "134", "line": 16, "column": 6, "nodeType": "135", "endLine": 16, "endColumn": 25, "suggestions": "136"}, {"ruleId": "133", "severity": 1, "message": "137", "line": 24, "column": 6, "nodeType": "135", "endLine": 24, "endColumn": 10, "suggestions": "138"}, {"ruleId": "129", "severity": 1, "message": "139", "line": 46, "column": 9, "nodeType": "131", "messageId": "132", "endLine": 46, "endColumn": 27}, {"ruleId": "133", "severity": 1, "message": "140", "line": 23, "column": 6, "nodeType": "135", "endLine": 23, "endColumn": 30, "suggestions": "141"}, {"ruleId": "133", "severity": 1, "message": "142", "line": 186, "column": 6, "nodeType": "135", "endLine": 186, "endColumn": 47, "suggestions": "143"}, {"ruleId": "133", "severity": 1, "message": "144", "line": 33, "column": 6, "nodeType": "135", "endLine": 33, "endColumn": 22, "suggestions": "145"}, {"ruleId": "129", "severity": 1, "message": "146", "line": 114, "column": 13, "nodeType": "131", "messageId": "132", "endLine": 114, "endColumn": 21}, {"ruleId": "129", "severity": 1, "message": "147", "line": 19, "column": 10, "nodeType": "131", "messageId": "132", "endLine": 19, "endColumn": 15}, {"ruleId": "129", "severity": 1, "message": "148", "line": 14, "column": 5, "nodeType": "131", "messageId": "132", "endLine": 14, "endColumn": 16}, {"ruleId": "133", "severity": 1, "message": "149", "line": 34, "column": 6, "nodeType": "135", "endLine": 34, "endColumn": 18, "suggestions": "150"}, {"ruleId": "133", "severity": 1, "message": "151", "line": 80, "column": 6, "nodeType": "135", "endLine": 80, "endColumn": 32, "suggestions": "152"}, "no-unused-vars", "'FiPhone' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDoctors'. Either include it or remove the dependency array.", "ArrayExpression", ["153"], "React Hook useEffect has a missing dependency: 'fetchDoctorProfile'. Either include it or remove the dependency array.", ["154"], "'formatAvailability' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAvailableSlots'. Either include it or remove the dependency array.", ["155"], "React Hook useEffect has a missing dependency: 'addNotification'. Either include it or remove the dependency array.", ["156"], "React Hook useEffect has a missing dependency: 'joinHelpline'. Either include it or remove the dependency array.", ["157"], "'response' is assigned a value but never used.", "'error' is assigned a value but never used.", "'localStream' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchMessages' and 'joinChat'. Either include them or remove the dependency array.", ["158"], "React Hook useCallback has a missing dependency: 'iceServers'. Either include it or remove the dependency array.", ["159"], {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, {"desc": "164", "fix": "165"}, {"desc": "166", "fix": "167"}, {"desc": "168", "fix": "169"}, {"desc": "170", "fix": "171"}, {"desc": "172", "fix": "173"}, "Update the dependencies array to be: [fetchDoctors, selectedSpecialty]", {"range": "174", "text": "175"}, "Update the dependencies array to be: [fetchDoctorProfile, id]", {"range": "176", "text": "177"}, "Update the dependencies array to be: [selectedDate, doctorId, fetchAvailableSlots]", {"range": "178", "text": "179"}, "Update the dependencies array to be: [isAuthenticated, token, user, isLoading, addNotification]", {"range": "180", "text": "181"}, "Update the dependencies array to be: [isOpen, joinHelpline, socket]", {"range": "182", "text": "183"}, "Update the dependencies array to be: [fetchMessages, joinChat, receiverId]", {"range": "184", "text": "185"}, "Update the dependencies array to be: [iceServers, roomId, sendICECandidate]", {"range": "186", "text": "187"}, [552, 571], "[fetchDoctors, selectedSpecialty]", [965, 969], "[fetchDoctorProfile, id]", [820, 844], "[selectedDate, doctorId, fetchAvailableSlots]", [5661, 5702], "[isAuthenticated, token, user, isLoading, addNotification]", [1170, 1186], "[isOpen, joinHelpline, socket]", [1048, 1060], "[fetchMessages, joinChat, receiverId]", [2745, 2771], "[iceServers, roomId, sendICECandidate]"]