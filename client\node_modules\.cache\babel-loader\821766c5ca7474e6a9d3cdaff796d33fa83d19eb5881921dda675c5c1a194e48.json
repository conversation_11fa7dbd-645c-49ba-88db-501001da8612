{"ast": null, "code": "var _jsxFileName = \"E:\\\\03\\\\client\\\\src\\\\components\\\\DoctorCard.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoctorCard = ({\n  doctor\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card-hover\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0\",\n        children: doctor.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: doctor.profileImage,\n          alt: doctor.user.name,\n          className: \"w-16 h-16 rounded-full object-cover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl font-semibold text-gray-600\",\n          children: doctor.user.name.charAt(0).toUpperCase()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 truncate\",\n          children: [\"Dr. \", doctor.user.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-primary-600 font-medium\",\n          children: doctor.specialty\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 text-sm mt-1\",\n          children: [doctor.experience, \" years experience\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700 text-sm mt-2 line-clamp-2\",\n          children: Array.isArray(doctor.qualifications) ? doctor.qualifications.join(', ') : doctor.qualifications\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), doctor.bio && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 text-sm mt-2 line-clamp-2\",\n          children: doctor.bio\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-right flex-shrink-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-gray-900\",\n          children: [\"$\", doctor.consultationFee]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500 mb-3\",\n          children: \"Consultation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/doctor/${doctor._id}`,\n          className: \"btn-primary text-sm\",\n          children: \"Book Now\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 pt-4 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-2 h-2 bg-green-500 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Available today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Next available: Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = DoctorCard;\nexport default DoctorCard;\nvar _c;\n$RefreshReg$(_c, \"DoctorCard\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "doctor", "className", "children", "profileImage", "src", "alt", "user", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "specialty", "experience", "Array", "isArray", "qualifications", "join", "bio", "consultationFee", "to", "_id", "_c", "$RefreshReg$"], "sources": ["E:/03/client/src/components/DoctorCard.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst DoctorCard = ({ doctor }) => {\n  return (\n    <div className=\"card-hover\">\n      <div className=\"flex items-start space-x-4\">\n        {/* Doctor Image */}\n        <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0\">\n          {doctor.profileImage ? (\n            <img \n              src={doctor.profileImage} \n              alt={doctor.user.name}\n              className=\"w-16 h-16 rounded-full object-cover\"\n            />\n          ) : (\n            <span className=\"text-2xl font-semibold text-gray-600\">\n              {doctor.user.name.charAt(0).toUpperCase()}\n            </span>\n          )}\n        </div>\n\n        {/* Doctor Info */}\n        <div className=\"flex-1 min-w-0\">\n          <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n            Dr. {doctor.user.name}\n          </h3>\n          \n          <p className=\"text-primary-600 font-medium\">\n            {doctor.specialty}\n          </p>\n          \n          <p className=\"text-gray-600 text-sm mt-1\">\n            {doctor.experience} years experience\n          </p>\n\n          <p className=\"text-gray-700 text-sm mt-2 line-clamp-2\">\n            {Array.isArray(doctor.qualifications)\n              ? doctor.qualifications.join(', ')\n              : doctor.qualifications}\n          </p>\n\n          {doctor.bio && (\n            <p className=\"text-gray-600 text-sm mt-2 line-clamp-2\">\n              {doctor.bio}\n            </p>\n          )}\n        </div>\n\n        {/* Consultation Fee & Action */}\n        <div className=\"text-right flex-shrink-0\">\n          <div className=\"text-lg font-bold text-gray-900\">\n            ${doctor.consultationFee}\n          </div>\n          <div className=\"text-sm text-gray-500 mb-3\">\n            Consultation\n          </div>\n          \n          <Link \n            to={`/doctor/${doctor._id}`}\n            className=\"btn-primary text-sm\"\n          >\n            Book Now\n          </Link>\n        </div>\n      </div>\n      \n      {/* Availability Indicator */}\n      <div className=\"mt-4 pt-4 border-t border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n            <span className=\"text-sm text-gray-600\">Available today</span>\n          </div>\n          \n          <div className=\"text-sm text-gray-500\">\n            Next available: Today\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DoctorCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EACjC,oBACEF,OAAA;IAAKG,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBJ,OAAA;MAAKG,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBAEzCJ,OAAA;QAAKG,SAAS,EAAC,mFAAmF;QAAAC,QAAA,EAC/FF,MAAM,CAACG,YAAY,gBAClBL,OAAA;UACEM,GAAG,EAAEJ,MAAM,CAACG,YAAa;UACzBE,GAAG,EAAEL,MAAM,CAACM,IAAI,CAACC,IAAK;UACtBN,SAAS,EAAC;QAAqC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,gBAEFb,OAAA;UAAMG,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EACnDF,MAAM,CAACM,IAAI,CAACC,IAAI,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNb,OAAA;QAAKG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BJ,OAAA;UAAIG,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,MACvD,EAACF,MAAM,CAACM,IAAI,CAACC,IAAI;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAELb,OAAA;UAAGG,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EACxCF,MAAM,CAACc;QAAS;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEJb,OAAA;UAAGG,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GACtCF,MAAM,CAACe,UAAU,EAAC,mBACrB;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJb,OAAA;UAAGG,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACnDc,KAAK,CAACC,OAAO,CAACjB,MAAM,CAACkB,cAAc,CAAC,GACjClB,MAAM,CAACkB,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,GAChCnB,MAAM,CAACkB;QAAc;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,EAEHX,MAAM,CAACoB,GAAG,iBACTtB,OAAA;UAAGG,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACnDF,MAAM,CAACoB;QAAG;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNb,OAAA;QAAKG,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvCJ,OAAA;UAAKG,SAAS,EAAC,iCAAiC;UAAAC,QAAA,GAAC,GAC9C,EAACF,MAAM,CAACqB,eAAe;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACNb,OAAA;UAAKG,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE5C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAENb,OAAA,CAACF,IAAI;UACH0B,EAAE,EAAE,WAAWtB,MAAM,CAACuB,GAAG,EAAG;UAC5BtB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAChC;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNb,OAAA;MAAKG,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDJ,OAAA;QAAKG,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDJ,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CJ,OAAA;YAAKG,SAAS,EAAC;UAAmC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzDb,OAAA;YAAMG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAENb,OAAA;UAAKG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAEvC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,EAAA,GA/EIzB,UAAU;AAiFhB,eAAeA,UAAU;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}